import React from "react";
import { useState, useEffect, useContext } from "react";
import siteConstant from "../../../helpers/constant/siteConstant.js";
import RotatingIcons from "./RotatingIcons.jsx";
import ConnectDialog from "../brands/ConnectPopup.jsx";
import apiInstance from "../../../helpers/Axios/axiosINstance.jsx";
import { URL } from "../../../helpers/constant/Url.js";
import { IntlContext } from "../../../App.jsx";
import { usePlatform } from "../../../helpers/context/PlatformContext.jsx";
import { setApiMessage } from "../../../helpers/context/toaster.jsx";
import { useBrand } from "../../../helpers/context/BrandContext.jsx";

function ConnectPlatform({ platformId = "twitter" }) {
  const [open, setOpen] = useState(false);
  const [connected, setConnected] = useState(false);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const { platformName, setPlatformName } = usePlatform();
  const { selectedBrand } = useBrand();
  const BrandId = localStorage.getItem("BrandId");

  const platformConfig = {
    twitter: {
      name: "Twitter",
      color: "#000000",
      icon: siteConstant?.SOCIAL_ICONS?.TWITTER_ICON,
    },
    facebook: {
      name: "Facebook",
      color: "#1877F2",
      icon: siteConstant?.SOCIAL_ICONS?.FACEBOOK_ICON,
    },
    instagram: {
      name: "Instagram",
      color: "#E4405F",
      icon: siteConstant?.SOCIAL_ICONS?.INSTAGRAM_ICON,
    },
    linkedin: {
      name: "LinkedIn",
      color: "#0A66C2",
      icon: siteConstant?.SOCIAL_ICONS?.LINKEDIN_ICON,
    },
    youtube: {
      name: "YouTube",
      color: "#FF0000",
      icon: siteConstant?.SOCIAL_ICONS?.YOUTUBE_ICON,
    },
    pinterest: {
      name: "Pinterest",
      color: "#E60023",
      icon: siteConstant?.SOCIAL_ICONS?.PINTEREST_ICON,
    },
    thread: {
      name: "Thread",
      color: "#000000",
      icon: siteConstant?.SOCIAL_ICONS?.THREADS_ICON,
    },
    tiktok: {
      name: "TikTok",
      color: "#000000",
      icon: siteConstant?.SOCIAL_ICONS?.TIKTOK_ICON,
    },
    reddit: {
      name: "Reddit",
      color: "#FF4500",
      icon: siteConstant?.SOCIAL_ICONS?.REDDIT_ICON,
    },
    tumblr: {
      name: "Tumblr",
      color: "#001935",
      icon: siteConstant?.SOCIAL_ICONS?.TUMBLR_ICON,
    },
    vimeo: {
      name: "vimeo",
      color: "#1EB8EB",
      icon: siteConstant?.SOCIAL_ICONS?.VIMEO_ICON,
    },
    x: {
      name: "X",
      color: "#000000",
      icon: siteConstant?.SOCIAL_ICONS?.TWITTER_ICON,
    },
    telegram: {
      name: "Telegram",
      color: "#0088cc",
      icon: siteConstant?.SOCIAL_ICONS?.TELEGRAM,
    },
    mastodon: {
      name: "Mastodon",
      color: "#6364FF",
      icon: siteConstant?.SOCIAL_ICONS?.MASTODON,
    },
  };

  const platform = platformConfig[platformId] || {
    name: "Platform",
    color: "#000000",
    icon: null,
  };

  // Check connection status for this platform when component mounts
  useEffect(() => {
    checkConnectionStatus();
  }, [platformId, selectedBrand]);

  // Check if the platform is connected
  const checkConnectionStatus = async () => {
    try {
      const response = await apiInstance.get(URL.GET_THIRDPARTY, {
        headers: {
          brand: BrandId,
        },
      });

      if (response?.data?.status) {
        const statusData = response?.data?.data;
        // Set connected status based on the response
        setConnected(!!statusData[platformId]);

        // Update local storage with connection status
        let userdata = JSON.parse(localStorage.getItem("userdata")) || {};
        userdata = { ...userdata, ...statusData };
        localStorage.setItem("userdata", JSON.stringify(userdata));
      }
    } catch (error) {
      console.error("Error checking platform connection status:", error);
    }
  };

  // Handle API call for connection or disconnection
  const handleApiCall = async (action) => {
    try {
      // Determine which API URL to use based on the action and platform
      const apiUrl =
        action === "disconnect"
          ? URL[`DISCONNECT_${platformId.toUpperCase()}`]
          : URL[`${platformId.toUpperCase()}`];

      let response;

      if (platformId === "mastodon" && action === "connect") {
        const formData = new FormData();
        formData.append("instance_url", "https://mastodon.social");
        response = await apiInstance.post(apiUrl, formData, {
          headers: {
            brand: BrandId,
          },
        });
      } else if (platformId === "telegram" && action === "disconnect") {
        // Special case for Telegram disconnect - use POST method
        response = await apiInstance.post(
          apiUrl,
          {},
          {
            headers: {
              brand: BrandId,
            },
          }
        );
      } else {
        response = await apiInstance.get(apiUrl, {
          headers: {
            brand: BrandId,
          },
        });
      }

      if (response?.status) {
        if (action === "connect" && response.data?.url) {
          // Redirect to the authentication URL if provided
          window.location.href = response.data?.url;
        } else {
          // Update connection status
          checkConnectionStatus();
          setApiMessage("error", response.data.message);
        }

        // Dispatch event to notify other components about status change
        const statusChangeEvent = new CustomEvent("channelStatusChange", {
          detail: {
            channelName: platformId,
            status: action === "connect",
          },
        });
        window.dispatchEvent(statusChangeEvent);
      } else {
        setApiMessage("error", response.data.message);
        console.log("Error:", response.data.message);
      }
    } catch (error) {
      setApiMessage("error");
    }
  };

  // Handle the connect confirmation
  const handleConnectConfirm = () => {
    handleApiCall("connect");
    setOpen(false);
  };

  // Handle disconnect confirmation
  const handleDisconnect = () => {
    handleApiCall("disconnect");
    // Update local storage
    const userdata = JSON.parse(localStorage.getItem("userdata")) || {};
    userdata[platformId] = false;
    localStorage.setItem("userdata", JSON.stringify(userdata));
  };

  const handleConnect = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  // Listen for channel status change events
  useEffect(() => {
    const handleStatusChange = (event) => {
      const { channelName, status } = event.detail;
      if (channelName === platformId) {
        setConnected(status);
      }
    };

    window.addEventListener("channelStatusChange", handleStatusChange);

    return () => {
      window.removeEventListener("channelStatusChange", handleStatusChange);
    };
  }, [platformId, selectedBrand]);

  return (
    <div className="bg-white min-h-[85vh] w-full p-4 sm:p-6 font-Ubuntu rounded-[12px] relative">
      <div className="mx-auto ms-[20px]">
        {/* Header with platform icon and name */}
        <div className="flex items-center space-x-3 mb-4">
          <div
            className="rounded-full w-10 h-10 flex items-center justify-center"
            style={{ backgroundColor: platform.color }}
          >
            {platform.icon ? (
              <img
                src={platform.icon}
                alt={platform.name}
                className="w-6 h-6 object-contain rounded-full"
              />
            ) : (
              <span className="text-white text-xs font-bold">
                {platform.name.charAt(0)}
              </span>
            )}
          </div>
          <h1 className="text-xl font-medium text-[#000000]">
            {platform.name}
          </h1>
        </div>

        {/* Main content section */}
        <div className="mb-6">
          <p className="text-[16px] xl:text-[20px] font-medium mb-2 text-[#000000]">
            Keep up with the important data from your community.
          </p>

          <p className="text-[16px] xl:text-[20px] font-normal text-[#000000] mb-6 mt-5 2xl:pr-[300px] lg:pr-[100px]">
            Enhance your presence on {platform.name} and make informed
            decisions. Understand the performance of your posts to refine your
            social strategy and achieve better outcomes.
          </p>

          {/* Benefits list */}
          <ul className="list-disc pl-5 mb-6 mt-12 space-y-6">
            <li className="text-[16px] xl:text-[18px] font-normal text-[#000000]">
              Schedule your posts and threads
            </li>
            <li className="text-[16px] xl:text-[18px] font-normal text-[#000000]">
              Monitor your competitors
            </li>
            <li className="text-[16px] xl:text-[18px] font-normal text-[#000000]">
              Gain insights on how many people view your profile, the number of
              views you receive and other important data
            </li>
          </ul>
        </div>

        {/* Bottom section with button and avatar circle */}
        <div className="flex flex-col lg:flex-row justify-between items-center relative">
          <div className="w-full lg:w-1/2">
            {/* Connect/Disconnect button */}
            <div className="w-full">
              <button
                onClick={handleConnect}
                className="px-8 py-4 rounded-[12px] w-full lg:w-auto flex items-center justify-between space-x-2 text-white mt-4"
                style={{ backgroundColor: platform.color }}
              >
                <span>Connect a {platform.name} account</span>
              </button>
            </div>

            {/* Connect Dialog */}
            <ConnectDialog
              open={open}
              onClose={handleClose}
              platform={platformId}
              onConfirm={handleConnectConfirm}
            />
          </div>

          {/* Circle with avatars */}
          <div className="w-full lg:w-1/2 mt-8 lg:mt-0 lg:mr-40 flex justify-center lg:justify-end">
            <div className="relative">
              <RotatingIcons />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ConnectPlatform;
