import React, { useContext, useEffect, useState } from "react";
import { IntlContext } from "../../../App.jsx";
import apiInstance from "../../../helpers/Axios/axiosINstance.jsx";

import { usePlatform } from "../../../helpers/context/PlatformContext";
import ConnectDialog from "../../components/brands/ConnectPopup.jsx";

import DisconnectDialog from "../../components/brands/DisconnectPopup.jsx";
import { useBrand } from "../../../helpers/context/BrandContext.jsx";

import Spinner from "../../../helpers/UI/Spinner.jsx";

import { setApiMessage } from "../../../helpers/context/toaster";

// Import your platform icons
import Facebook from "../../../assets/images/Analytics/facebook.svg";
import PinterestLogo from "../../../assets/images/Analytics/pinterest.svg";
import VimoLogo from "../../../assets/images/Analytics/vimeo.svg";
import LinkedInLogo from "../../../assets/images/Analytics/linkedIn.svg";
import TwitterLogo from "../../../assets/images/Analytics/X.svg";
import TiktokLogo from "../../../assets/images/Analytics/tiktok.svg";
import RedditLogo from "../../../assets/images/Analytics/reddit.svg";
import ThreadsLogo from "../../../assets/images/Analytics/thread.svg";
import InstagramLogo from "../../../assets/images/Analytics/instagram.svg";
import YoutubeLogo from "../../../assets/images/Analytics/youtube.svg";
import TumblrLogo from "../../../assets/images/Analytics/tumblr.svg";
import addBrand from "../../../assets/images/svg_icon/addBrand.svg";
import dummyProfile from "../../../assets/images/dummtprofile.png";
import Cancel from "../../../assets/images/Analytics/Cance_icon.svg";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
} from "@mui/material";
import { URL } from "../../../helpers/constant/Url.js";
// import { URL } from "../../../helpers/constant/Url.js";
import Mastodon from "../../../assets/images/Analytics/mastodon.svg";
import Telegram from "../../../assets/images/Analytics/telegram.svg";

const platformConfig = {
  facebook: { color: "#0866FF", icon: Facebook },
  pinterest: { color: "#E60023", icon: PinterestLogo },
  vimeo: { color: "#1EB8EB", icon: VimoLogo },
  linkedin: { color: "#0A66C2", icon: LinkedInLogo },
  twitter: { color: "#100E0F", icon: TwitterLogo },
  tiktok: { color: "#000000", icon: TiktokLogo },
  reddit: { color: "#FC471E", icon: RedditLogo },
  thread: { color: "#000000", icon: ThreadsLogo },
  instagram: { color: "#C30FB2", icon: InstagramLogo },
  youtube: { color: "#FF0302", icon: YoutubeLogo },
  tumblr: { color: "#35465C", icon: TumblrLogo },
  x: { color: "#000000", icon: TwitterLogo },
  telegram: { color: "#0088cc", icon: Telegram },
  mastodon: { color: "#6364FF", icon: Mastodon },
};

function ChannelConnect() {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const { selectedBrand } = useBrand();
  const [channels, setChannels] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState(null);
  const { platformName, setPlatformName } = usePlatform();
  const [connectedPlatforms, setConnectedPlatforms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [profileLoading, setProfileLoading] = useState(true);

  // console.log("Connected Platforms from Dashboard ===>", connectedPlatforms);
  const BrandId = selectedBrand?.id;

  // Add state for ConnectDialog
  const [showConnectDialog, setShowConnectDialog] = useState(false);

  const checkThirdPartyStatus = async () => {
    if (!selectedBrand?.id) return;
    try {
      setLoading(true);
      const response = await apiInstance.get(URL.GET_THIRDPARTY, {
        headers: {
          brand: BrandId,
        },
      });

      if (response?.data?.status) {
        const statusData = response?.data?.data;
        console.log("Status data from API:", statusData);

        const updatedChannels = intlContext?.channels?.map((channel) => {
          const channelKey = channel?.name;
          const lowerCaseKey = channel?.name?.toLowerCase();
          const capitalizedKey =
            channel?.name?.charAt(0).toUpperCase() +
            channel?.name?.slice(1).toLowerCase();

          let isConnected = false;
          if (statusData[channelKey] !== undefined) {
            isConnected = statusData[channelKey];
          } else if (statusData[lowerCaseKey] !== undefined) {
            isConnected = statusData[lowerCaseKey];
          } else if (statusData[capitalizedKey] !== undefined) {
            isConnected = statusData[capitalizedKey];
          }

          return {
            ...channel,
            status: isConnected
              ? localesData?.USER_WEB?.CHANNELS?.DISCONNECT
              : localesData?.USER_WEB?.CHANNELS?.CONNECT,
            isConnected: isConnected,
          };
        });

        setChannels(updatedChannels);

        // Also stash statusData locally if needed
        let userdata = JSON.parse(localStorage.getItem("userdata")) || {};
        userdata = { ...userdata, ...statusData };
        localStorage.setItem("userdata", JSON.stringify(userdata));
      }
    } catch (error) {
      console.error("Error checking third-party status:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      if (!BrandId) {
        if (isMounted) {
          setConnectedPlatforms([]);
        }
        return;
      }

      try {
        setProfileLoading(true);
        const response = await apiInstance.get(URL.SHARE_PROFILE, {
          headers: {
            brand: BrandId,
          },
        });

        if (isMounted) {
          setConnectedPlatforms(response?.data?.profile?.social_links || []);
        }
      } catch (error) {
        console.error("Error processing request:", error);
        if (isMounted && error.response?.status === 500) {
          // Retry after delay
          setTimeout(fetchData, 1000);
        }
      } finally {
        if (isMounted) {
          setProfileLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [BrandId]);

  const handleApiCall = async (apiUrl, index, action) => {
    try {
      let response;
      const platformName = selectedChannel?.name?.toLowerCase().trim();
      // Debug log
      console.log("handleApiCall:", { platformName, action, apiUrl });
      // Special case for Mastodon connect
      if (platformName === "mastodon" && action === "connect") {
        console.log("Using POST for Mastodon connect");
        const formData = new FormData();
        formData.append("instance_url", "https://mastodon.social");
        response = await apiInstance.post(apiUrl, formData, {
          headers: {
            brand: BrandId,
          },
        });
      } else if (platformName === "telegram" && action === "disconnect") {
        // Special case for Telegram disconnect - use POST method
        console.log("Using POST for Telegram disconnect");
        response = await apiInstance.post(
          apiUrl,
          {},
          {
            headers: {
              brand: BrandId,
            },
          }
        );
      } else {
        console.log("Using GET for", platformName, action);
        response = await apiInstance.get(apiUrl, {
          headers: {
            brand: BrandId,
          },
        });
      }
      if (response?.status) {
        if (action === "connect" && response.data?.url) {
          window.location.href = response.data?.url;
        } else {
          checkThirdPartyStatus();
        }

        const statusChangeEvent = new CustomEvent("channelStatusChange", {
          detail: {
            channelName: selectedChannel.name,
            status: action === "connect",
          },
        });
        window.dispatchEvent(statusChangeEvent);
      } else {
        console.error("Error:", response?.message || "Something went wrong");
      }
    } catch (error) {
      console.error("Error processing request:", error);
    }
  };

  useEffect(() => {
    const handleStatusChange = (event) => {
      const { channelName, status } = event.detail;

      setChannels((prevChannels) =>
        prevChannels.map((channel) =>
          channel.name === channelName
            ? {
                ...channel,
                status: status
                  ? localesData?.USER_WEB?.CHANNELS?.CONNECT
                  : localesData?.USER_WEB?.CHANNELS?.DISCONNECT,
                isConnected: status,
              }
            : channel
        )
      );
    };

    window.addEventListener("channelStatusChange", handleStatusChange);

    return () => {
      window.removeEventListener("channelStatusChange", handleStatusChange);
    };
  }, [localesData]);

  const openModal = (channel) => {
    setSelectedChannel(channel);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedChannel(null);
  };

  const openConnectDialog = (channel) => {
    setSelectedChannel(channel);
    setShowConnectDialog(true);
    console.log("Opening dialog for platform:", channel?.name);
  };

  const closeConnectDialog = () => {
    setShowConnectDialog(false);
    setSelectedChannel(null);
  };

  const handleConnectConfirm = () => {
    if (selectedChannel) {
      const normalizedName =
        selectedChannel.name.toLowerCase() === "threads"
          ? "thread"
          : selectedChannel.name;
      const apiUrl = URL[`${normalizedName.toUpperCase()}`];
      handleApiCall(apiUrl, channels.indexOf(selectedChannel), "connect");
    }
    closeConnectDialog();
  };

  const confirmAction = (action) => {
    if (selectedChannel) {
      const normalizedName =
        selectedChannel.name.toLowerCase() === "threads"
          ? "thread"
          : selectedChannel.name;
      const apiUrl =
        action === "disconnect"
          ? URL[`DISCONNECT_${normalizedName.toUpperCase()}`]
          : URL[`INSTAGRAM`];

      handleApiCall(apiUrl, channels.indexOf(selectedChannel), action);
      if (action === "disconnect" && selectedChannel.name) {
        const userdata = JSON.parse(localStorage.getItem("userdata")) || {};
        userdata[selectedChannel.name] = false;
        localStorage.setItem("userdata", JSON.stringify(userdata));
      }
      closeModal();
    }
  };

  useEffect(() => {
    checkThirdPartyStatus();
    if (intlContext?.channels) {
      console.log(
        "Available channels:",
        intlContext.channels.map((ch) => ch.name)
      );
    }
  }, [selectedBrand?.id]);

  // Limit to first 5 channels
  const displayChannels = channels.slice(0, 5);

  return (
    <div className="bg-[#F9F9F9] border border-[#E0E0E0] rounded-[12px] p-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-[#563D39] font-medium text-base">
          Your Connections
        </h3>
        <Link
          to="/brands"
          state={{ activeView: "platforms" }}
          className="text-[12px] md:text-[16px] font-medium text-[#563D39] hover:no-underline p-1"
        >
          View all
        </Link>
      </div>

      {/* Show spinner while loading */}
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <Spinner />
        </div>
      ) : (
        /* Platform List */
        <div className="space-y-4 ">
          {profileLoading
            ? Array.from({ length: 5 }).map((_, i) => (
                <div
                  key={i}
                  className="animate-pulse flex items-center h-[69px] w-full bg-white rounded-[12px] overflow-hidden"
                >
                  <div className="h-full w-[45px] bg-gray-200"></div>

                  <div className="flex-1 ml-9 md:ml-8">
                    <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                  </div>

                  <div className="mr-4 shrink-0">
                    <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  </div>
                </div>
              ))
            : displayChannels.map((channel, index) => {
                const config = platformConfig[channel?.name?.toLowerCase()];
                const isActive = channel.isConnected;

                if (!config) return null;

                return (
                  <div className="relative">
                    <div
                      key={index}
                      className=" flex items-center h-[69px] w-full bg-white rounded-[12px] overflow-hidden"
                    >
                      {/* Colored left section with platform icon */}
                      <div
                        className="flex items-center justify-center h-full w-[45px] relative"
                        style={{ backgroundColor: config.color }}
                      >
                        <div className="bg-white p-2 rounded-full absolute top-[12px] -right-[20px] shadow-lg">
                          <img
                            src={config.icon}
                            alt={channel?.name}
                            className="w-7 h-7"
                          />
                        </div>
                      </div>

                      {/* Platform name/username */}
                      <div className="flex-1 ml-9 md:ml-8 text-sm font-medium text-gray-800 truncate">
                        {isActive
                          ? connectedPlatforms.find(
                              (p) => p.platform === channel?.name?.toLowerCase()
                            )?.username || channel?.name
                          : channel?.name}
                      </div>

                      {/* Status/Action Button */}
                      <div className="mr-4 shrink-0 w-8 h-8">
                        {isActive ? (
                          <button
                            onClick={() => openModal(channel)}
                            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center"
                          >
                            <img
                              src={
                                connectedPlatforms.find(
                                  (p) =>
                                    p.platform === channel?.name?.toLowerCase()
                                )?.profile_image || dummyProfile
                              }
                              alt="Profile"
                              className="w-[32px] h-[32px] object-cover rounded-full"
                            />
                          </button>
                        ) : (
                          <button
                            onClick={() => openConnectDialog(channel)}
                            className="w-full h-full flex items-center justify-center"
                          >
                            <img
                              src={addBrand}
                              alt="+"
                              className="w-full h-full object-contain"
                            />
                          </button>
                        )}
                      </div>
                      {isActive && (
                        <div
                          className="h-4 w-6 absolute -top-[4px] -right-[12px] z-40 cursor-pointer"
                          onClick={() => openModal(channel)}
                        >
                          <img
                            src={Cancel}
                            alt=""
                            className=" rounded-full bg-Red h-4 w-4 p-1  "
                          />
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
        </div>
      )}

      {/* Disconnect Confirmation Modal */}
      <DisconnectDialog
        open={showModal}
        onClose={closeModal}
        platform={selectedChannel?.name?.toLowerCase()}
        onConfirm={() => confirmAction("disconnect")}
      />

      {/* Connect Dialog for platform connection */}
      <ConnectDialog
        open={showConnectDialog}
        onClose={closeConnectDialog}
        platform={
          selectedChannel?.name?.toLowerCase() === "threads"
            ? "thread"
            : selectedChannel?.name?.toLowerCase()
        }
        onConfirm={handleConnectConfirm}
      />
    </div>
  );
}

export default ChannelConnect;
